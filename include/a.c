#include "csi_nn.h"
#include "shl_memory.h"
#include "shl_public/shl_ref.h"
#include <sys/mman.h>

#define C(v)                                                                   \
  if (v != 1) {                                                                \
    printf("error: %s\n", #v);                                                 \
    exit(1);                                                                   \
  }
#define G(v)                                                                   \
  if (v <= 0) {                                                                \
    printf("error: %s\n", #v);                                                 \
    exit(1);                                                                   \
  }

struct csinn_session *csinn_() {
  struct csinn_session *sess = csinn_alloc_session();
  sess->base_run_mode = CSINN_RM_NPU_GRAPH;
  sess->base_api = CSINN_TH1520;
  sess->base_dtype = CSINN_DTYPE_FLOAT16;
  sess->dynamic_shape = false;
  csinn_session_init(sess);
  csinn_set_input_number(2, sess);
  csinn_set_output_number(1, sess);

  struct csinn_tensor *input_0 = csinn_alloc_tensor(sess);
  input_0->name = "input_0";
  input_0->mtype = CSINN_MEM_TYPE_CPU_ALIGNED;
  input_0->dtype = CSINN_DTYPE_FLOAT16;
  input_0->layout = CSINN_LAYOUT_NC;
  input_0->dim[0] = 1;
  input_0->dim[1] = 32;
  input_0->dim_count = 2;
  input_0->quant_channel = 0;
  input_0->quant_type = CSINN_QUANT_FLOAT16;
  struct csinn_tensor *input_1 = csinn_alloc_tensor(sess);
  input_1->name = "output_1";
  input_1->mtype = CSINN_MEM_TYPE_CPU_ALIGNED;
  input_1->dtype = CSINN_DTYPE_FLOAT16;
  input_1->layout = CSINN_LAYOUT_NC;
  input_1->dim[0] = 32;
  input_1->dim[1] = 1;
  input_1->dim_count = 2;
  input_1->quant_channel = 0;
  input_0->quant_type = CSINN_QUANT_FLOAT16;

  struct csinn_tensor *mul_0 = csinn_alloc_tensor(sess);
  mul_0->name = "matmul_0";
  mul_0->mtype = CSINN_MEM_TYPE_CPU_ALIGNED;
  mul_0->dtype = CSINN_DTYPE_FLOAT16;
  mul_0->layout = CSINN_LAYOUT_NC;
  mul_0->dim[0] = 1;
  mul_0->dim[1] = 1;
  mul_0->dim_count = 2;
  mul_0->quant_channel = 0;
  mul_0->quant_type = CSINN_QUANT_FLOAT16;
  struct csinn_diso_params *params_0 = csinn_alloc_params(sizeof(struct csinn_diso_params), sess);
  params_0->base.name = "mul";
  C(csinn_mul_init(input_0, input_1, mul_0, params_0));


  csinn_set_tensor_entry(input_0, sess);
  csinn_set_tensor_entry(input_1, sess);
  csinn_set_input(0, input_0, sess);
  csinn_set_input(1, input_1, sess);

  C(csinn_mul(input_0, input_1, mul_0, params_0));

  G(csinn_set_output(0, mul_0, sess));

  G(csinn_session_setup(sess));
  return sess;
}
void csinn_run(void *data0, void *data1, void *data2, struct csinn_session *sess) {
  struct csinn_tensor input0_tensor;
  input0_tensor.data = data0;
  G(csinn_update_input(0, &input0_tensor, sess));

  struct csinn_tensor input1_tensor;
  input1_tensor.data = data1;
  G(csinn_update_input(1, &input1_tensor, sess));

  struct csinn_tensor output0_tensor;
  G(csinn_session_run(sess));
  G(csinn_get_output(0, &output0_tensor, sess));
  struct csinn_tensor *foutput = shl_ref_tensor_transform_f32(&output0_tensor);
  for (int i=0; i<16; i++) {
    printf("%lf, ", ((double*)foutput->data)[i]);
  }
  printf("\n");
}

struct mem_io {
  float input0[0x400];
  float input1[0x400];
  float output[0x400];
};

int main(int argc, char **argv) {
  struct csinn_session *sess = csinn_();

  struct mem_io *mem_base = shl_mem_alloc_aligned(0x3000, 0x1000);

  for (int i = 0; i < 0x400; i++) {
    mem_base->input0[i] = (float)(2.f);
    mem_base->input1[i] = (float)(2.f);
  }
  uint64_t start_time, end_time;

  start_time = shl_get_timespec();
  csinn_run(mem_base->input0, mem_base->input1, mem_base->output, sess);
  end_time = shl_get_timespec();
  printf("Run graph execution time: %.5fms\nResult: ", ((float)(end_time - start_time)) / 1000000);

  csinn_session_deinit(sess);
  csinn_free_session(sess);
  munmap(mem_base, 0x1000);

  return 0;
}