/*
 * Copyright (C) 2016-2023 C-SKY Microsystems Co., Ltd. All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the License); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "csinn/csi_nn.h"
#include "shl_public/shl_ref.h"
#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include <string.h>
#include <stdbool.h>

// 测试工具函数
void init_testsuite(const char *testname) {
    printf("=== %s ===\n", testname);
}

int done_testing(void) {
    printf("=== Test completed ===\n");
    return 0;
}

// 计算余弦相似度
float compute_cs(float *a, float *b, uint32_t size) {
    double dot_sum = 0.0;
    double a_norm = 0.0;
    double b_norm = 0.0;

    for (int i = 0; i < size; i++) {
        dot_sum += (a[i] * b[i]);
        a_norm += (a[i] * a[i]);
        b_norm += (b[i] * b[i]);
    }
    return dot_sum / (sqrt(a_norm * b_norm));
}

// 结果验证函数
void result_verify_f32(float *reference, float *output, float *input,
                       float gap, int size, bool save) {
    float max_error = 0;
    int error_count = 0;

    for (int i = 0; i < size; i++) {
        float error = fabs(reference[i] - output[i]);
        if (error > gap) {
            error = fabs(reference[i] - output[i]) / fabs(reference[i] + 1e-9);
        }
        if (error > max_error) {
            max_error = error;
        }
        if (error > gap) {
            error_count++;
            printf("Error at %d: ref=%.6f, out=%.6f, diff=%.6f\n",
                   i, reference[i], output[i], error);
        }
    }

    printf("Max error: %f\n", max_error);
    float cs = compute_cs(output, reference, size);
    printf("Cosine similarity: %f\n", cs);

    if (cs >= gap && error_count == 0) {
        printf("✓ Test PASSED\n");
    } else {
        printf("✗ Test FAILED (errors: %d, cos_sim: %f)\n", error_count, cs);
    }
}

// NPU测试运行函数
void op_test_run_npu(struct csinn_tensor *input0, struct csinn_tensor *input1,
                     struct csinn_tensor *output, struct csinn_diso_params *params,
                     struct csinn_session *sess, struct csinn_tensor *real_input0,
                     struct csinn_tensor *real_input1, float *output_data, float diff) {

    printf("Running NPU graph test...\n");

    // 初始化会话
    csinn_session_init(sess);
    csinn_set_input_number(2, sess);
    csinn_set_output_number(1, sess);

    // 初始化操作
    csinn_mul_init(input0, input1, output, params);

    // 设置输入输出张量
    csinn_set_tensor_entry(input0, sess);
    csinn_set_tensor_entry(input1, sess);
    csinn_set_input(0, input0, sess);
    csinn_set_input(1, input1, sess);

    // 执行操作
    csinn_mul(input0, input1, output, params);

    // 设置输出并建立会话
    csinn_set_output(0, output, sess);
    csinn_session_setup(sess);

    // 更新输入数据并运行
    csinn_update_input(0, real_input0, sess);
    csinn_update_input(1, real_input1, sess);
    csinn_session_run(sess);

    // 获取输出结果
    csinn_get_output(0, output, sess);

    // 转换输出为float32进行验证
    struct csinn_tensor *foutput = shl_ref_tensor_transform_f32(output);
    result_verify_f32(output_data, foutput->data, (float*)input0->data, diff,
                      csinn_tensor_size(output), false);

    // 清理资源
    shl_ref_tensor_transform_free_f32(foutput);
    csinn_session_deinit(sess);
    csinn_free_session(sess);
}

// 数据类型转换函数
struct csinn_tensor *convert_f32_input(struct csinn_tensor *tensor, int dtype,
                                       struct csinn_session *sess) {
    struct csinn_tensor *ret = csinn_alloc_tensor(sess);
    ret->dim_count = tensor->dim_count;
    for (int i = 0; i < tensor->dim_count; i++) {
        ret->dim[i] = tensor->dim[i];
    }
    ret->dtype = dtype;
    ret->layout = tensor->layout;
    ret->name = tensor->name;
    ret->sess = sess;

    int size = csinn_tensor_size(ret);
    ret->data = malloc(size * sizeof(float));
    memcpy(ret->data, tensor->data, size * sizeof(float));

    return ret;
}

// 测试不同量化类型的函数
void test_i8_sym(struct csinn_tensor *input0, struct csinn_tensor *input1,
                 struct csinn_tensor *output, struct csinn_diso_params *params,
                 float difference) {
    printf("Testing i8 symmetric quantization\n");
    struct csinn_session *sess = csinn_alloc_session();
    sess->base_api = CSINN_TH1520;
    sess->base_quant_type = CSINN_QUANT_INT8_SYM;
    sess->base_run_mode = CSINN_RM_NPU_GRAPH;

    struct csinn_tensor *qinput0 = convert_f32_input(input0, CSINN_DTYPE_FLOAT32, sess);
    struct csinn_tensor *qinput1 = convert_f32_input(input1, CSINN_DTYPE_FLOAT32, sess);
    struct csinn_tensor *qoutput = convert_f32_input(output, CSINN_DTYPE_FLOAT32, sess);
    struct csinn_tensor *real_input0 = convert_f32_input(input0, CSINN_DTYPE_INT8, sess);
    struct csinn_tensor *real_input1 = convert_f32_input(input1, CSINN_DTYPE_INT8, sess);

    op_test_run_npu(qinput0, qinput1, qoutput, params, sess, real_input0, real_input1,
                    (float*)output->data, difference);
}

void test_i8_asym(struct csinn_tensor *input0, struct csinn_tensor *input1,
                  struct csinn_tensor *output, struct csinn_diso_params *params,
                  float difference) {
    printf("Testing i8 asymmetric quantization\n");
    struct csinn_session *sess = csinn_alloc_session();
    sess->base_api = CSINN_TH1520;
    sess->base_quant_type = CSINN_QUANT_INT8_ASYM;
    sess->base_run_mode = CSINN_RM_NPU_GRAPH;

    struct csinn_tensor *qinput0 = convert_f32_input(input0, CSINN_DTYPE_INT8, sess);
    struct csinn_tensor *qinput1 = convert_f32_input(input1, CSINN_DTYPE_INT8, sess);
    struct csinn_tensor *qoutput = convert_f32_input(output, CSINN_DTYPE_INT8, sess);
    struct csinn_tensor *real_input0 = convert_f32_input(input0, CSINN_DTYPE_INT8, sess);
    struct csinn_tensor *real_input1 = convert_f32_input(input1, CSINN_DTYPE_INT8, sess);

    op_test_run_npu(qinput0, qinput1, qoutput, params, sess, real_input0, real_input1,
                    (float*)output->data, difference);
}

void test_u8_asym(struct csinn_tensor *input0, struct csinn_tensor *input1,
                  struct csinn_tensor *output, struct csinn_diso_params *params,
                  float difference) {
    printf("Testing u8 asymmetric quantization\n");
    struct csinn_session *sess = csinn_alloc_session();
    sess->base_api = CSINN_TH1520;
    sess->base_quant_type = CSINN_QUANT_UINT8_ASYM;
    sess->base_run_mode = CSINN_RM_NPU_GRAPH;

    struct csinn_tensor *qinput0 = convert_f32_input(input0, CSINN_DTYPE_UINT8, sess);
    struct csinn_tensor *qinput1 = convert_f32_input(input1, CSINN_DTYPE_UINT8, sess);
    struct csinn_tensor *qoutput = convert_f32_input(output, CSINN_DTYPE_UINT8, sess);
    struct csinn_tensor *real_input0 = convert_f32_input(input0, CSINN_DTYPE_UINT8, sess);
    struct csinn_tensor *real_input1 = convert_f32_input(input1, CSINN_DTYPE_UINT8, sess);

    op_test_run_npu(qinput0, qinput1, qoutput, params, sess, real_input0, real_input1,
                    (float*)output->data, difference);
}

// 主测试函数
void test_npu_mul(struct csinn_tensor *input0, struct csinn_tensor *input1,
                  struct csinn_tensor *output, struct csinn_diso_params *params,
                  float difference) {
    printf("=== Testing NPU Multiplication Operation ===\n");
    params->base.api = CSINN_TH1520;
    params->base.layout = CSINN_LAYOUT_NCHW;

    // 测试不同的量化类型
    test_i8_sym(input0, input1, output, params, difference);
    test_i8_asym(input0, input1, output, params, difference);
    test_u8_asym(input0, input1, output, params, difference);
}

// 创建测试数据
void create_test_data(float *data, int size, int pattern) {
    switch (pattern) {
        case 0: // 递增序列
            for (int i = 0; i < size; i++) {
                data[i] = (float)i + 1.0f;
            }
            break;
        case 1: // 随机数据
            for (int i = 0; i < size; i++) {
                data[i] = (float)(rand() % 100) / 10.0f;
            }
            break;
        case 2: // 正弦波
            for (int i = 0; i < size; i++) {
                data[i] = sin(i * 0.1f) * 10.0f;
            }
            break;
        default:
            for (int i = 0; i < size; i++) {
                data[i] = 1.0f;
            }
    }
}

// 计算参考结果
void compute_reference_mul(float *input0, float *input1, float *output, int size) {
    for (int i = 0; i < size; i++) {
        output[i] = input0[i] * input1[i];
    }
}

int main(int argc, char **argv) {
    init_testsuite("NPU Multiplication Test Suite");

    // 测试参数
    int batch = 1, channel = 3, height = 4, width = 4;
    int size = batch * channel * height * width;
    float difference = argc > 1 ? atof(argv[1]) : 0.9f; // 余弦相似度阈值

    printf("Test configuration:\n");
    printf("  Tensor shape: [%d, %d, %d, %d]\n", batch, channel, height, width);
    printf("  Total elements: %d\n", size);
    printf("  Similarity threshold: %.3f\n", difference);

    // 分配内存
    float *input0_data = (float*)malloc(size * sizeof(float));
    float *input1_data = (float*)malloc(size * sizeof(float));
    float *output_data = (float*)malloc(size * sizeof(float));
    float *reference_data = (float*)malloc(size * sizeof(float));

    // 创建测试数据
    create_test_data(input0_data, size, 0); // 递增序列
    create_test_data(input1_data, size, 1); // 随机数据

    // 计算参考结果
    compute_reference_mul(input0_data, input1_data, reference_data, size);

    printf("\nSample input data:\n");
    printf("Input0: ");
    for (int i = 0; i < 8; i++) printf("%.2f ", input0_data[i]);
    printf("...\n");
    printf("Input1: ");
    for (int i = 0; i < 8; i++) printf("%.2f ", input1_data[i]);
    printf("...\n");
    printf("Reference: ");
    for (int i = 0; i < 8; i++) printf("%.2f ", reference_data[i]);
    printf("...\n\n");

    // 创建张量
    struct csinn_tensor *input0 = csinn_alloc_tensor(NULL);
    input0->dim[0] = batch;
    input0->dim[1] = channel;
    input0->dim[2] = height;
    input0->dim[3] = width;
    input0->dim_count = 4;
    input0->dtype = CSINN_DTYPE_FLOAT32;
    input0->layout = CSINN_LAYOUT_NCHW;
    input0->data = input0_data;
    input0->name = "input0";

    struct csinn_tensor *input1 = csinn_alloc_tensor(NULL);
    input1->dim[0] = batch;
    input1->dim[1] = channel;
    input1->dim[2] = height;
    input1->dim[3] = width;
    input1->dim_count = 4;
    input1->dtype = CSINN_DTYPE_FLOAT32;
    input1->layout = CSINN_LAYOUT_NCHW;
    input1->data = input1_data;
    input1->name = "input1";

    struct csinn_tensor *output = csinn_alloc_tensor(NULL);
    output->dim[0] = batch;
    output->dim[1] = channel;
    output->dim[2] = height;
    output->dim[3] = width;
    output->dim_count = 4;
    output->dtype = CSINN_DTYPE_FLOAT32;
    output->layout = CSINN_LAYOUT_NCHW;
    output->data = reference_data; // 使用参考数据进行验证
    output->name = "output";

    // 创建操作参数
    struct csinn_diso_params *params = csinn_alloc_params(sizeof(struct csinn_diso_params), NULL);
    params->base.name = "mul_params";
    params->base.layout = CSINN_LAYOUT_NCHW;

    // 运行测试
    test_npu_mul(input0, input1, output, params, difference);

    // 清理内存
    free(input0_data);
    free(input1_data);
    free(output_data);
    free(reference_data);

    return done_testing();
}