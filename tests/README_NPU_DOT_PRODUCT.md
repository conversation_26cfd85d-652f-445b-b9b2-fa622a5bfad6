# NPU向量点乘测试

这是一个简单的NPU向量点乘测试样例，使用TH1520 NPU实现向量的逐元素乘法操作。

## 文件说明

- `simple_npu_dot_product.c` - NPU向量点乘测试源代码
- `Makefile.dot` - 编译配置文件
- `simple_npu_dot_product` - 编译生成的可执行文件

## 编译方法

使用指定的clang编译器编译：

```bash
make -f Makefile.dot
```

编译参数说明：
- `CC = /home/<USER>/llvm/bin/clang` - 使用指定的clang编译器
- `INCLUDE = -I../include -I./utils -I ../include/shl_public` - 包含头文件路径
- `CFLAGS = -O2 -g -Wl,--unresolved-symbols=ignore-all` - 编译选项
- `LIBS = -lm` - 链接数学库

## 运行方法

编译成功后，将可执行文件复制到远程服务器运行：

```bash
# 默认运行（相似度阈值0.95）
./simple_npu_dot_product

# 指定相似度阈值
./simple_npu_dot_product 0.9
```

## 测试功能

### 主要功能
1. **NPU会话管理** - 创建和配置TH1520 NPU会话
2. **张量操作** - 创建1D向量张量，配置数据类型和布局
3. **向量点乘** - 使用NPU执行逐元素乘法操作
4. **量化支持** - 支持INT8对称量化
5. **结果验证** - 使用余弦相似度验证NPU计算结果

### 测试流程
1. 初始化测试数据（1024个元素的向量）
2. 创建NPU会话和张量
3. 配置INT8对称量化
4. 执行NPU向量点乘操作
5. 验证结果精度
6. 清理资源

### 测试配置
- **向量大小**: 1024个元素
- **数据类型**: FLOAT32 输入，INT8 量化
- **NPU API**: TH1520
- **量化类型**: INT8对称量化
- **布局**: CSINN_LAYOUT_N (1D向量)
- **默认阈值**: 0.95 (余弦相似度)

## 代码结构

### 核心函数

1. **npu_dot_product_test_run()** - NPU测试执行函数
   - 会话初始化和配置
   - 张量设置和操作执行
   - 结果获取和验证

2. **test_dot_product_i8_sym()** - INT8对称量化测试
   - 创建量化会话
   - 数据类型转换
   - 调用NPU执行函数

3. **compute_dot_product_reference()** - 计算参考结果
   - CPU端逐元素乘法
   - 用于验证NPU计算结果

4. **result_verify_f32()** - 结果验证函数
   - 计算余弦相似度
   - 误差统计和报告

### NPU操作流程

```c
// 1. 创建会话
struct csinn_session *sess = csinn_alloc_session();
sess->base_api = CSINN_TH1520;
sess->base_quant_type = CSINN_QUANT_INT8_SYM;
sess->base_run_mode = CSINN_RM_NPU_GRAPH;

// 2. 初始化会话
csinn_session_init(sess);
csinn_set_input_number(2, sess);
csinn_set_output_number(1, sess);

// 3. 初始化乘法操作
csinn_mul_init(input0, input1, output, params);

// 4. 设置输入输出
csinn_set_tensor_entry(input0, sess);
csinn_set_tensor_entry(input1, sess);
csinn_set_input(0, input0, sess);
csinn_set_input(1, input1, sess);

// 5. 执行操作
csinn_mul(input0, input1, output, params);

// 6. 建立会话并运行
csinn_set_output(0, output, sess);
csinn_session_setup(sess);
csinn_update_input(0, real_input0, sess);
csinn_update_input(1, real_input1, sess);
csinn_session_run(sess);

// 7. 获取结果
csinn_get_output(0, output, sess);
```

## 输出示例

```
=== NPU Vector Dot Product Test ===
Test Configuration:
  Vector size: 1024
  Similarity threshold: 0.950
  NPU API: TH1520
  Operation: Element-wise multiplication (dot product)

Sample Data (first 8 elements):
  Vector A: 0.100 0.200 0.300 0.400 0.500 0.600 0.700 0.800 
  Vector B: 0.200 0.400 0.600 0.800 1.000 1.200 1.400 1.600 
  Reference: 0.020 0.080 0.180 0.320 0.500 0.720 0.980 1.280 

=== NPU Dot Product Test ===
Testing dot product with INT8 symmetric quantization
Running NPU dot product test...
  Max error: 0.000000
  Cosine similarity: 1.000000
  ✓ Test PASSED

=== NPU Dot Product Test Completed ===
=== Test completed ===
```

## 注意事项

1. **编译环境** - 需要使用指定的clang编译器路径
2. **头文件依赖** - 确保CSI-NN2头文件路径正确
3. **运行环境** - 需要在支持TH1520 NPU的环境中运行
4. **内存管理** - 代码包含完整的内存分配和释放
5. **错误处理** - 包含NPU操作的错误检查和报告

## 扩展说明

这个测试样例可以作为基础模板，扩展支持：
- 更多的量化类型（INT8非对称、UINT8等）
- 不同的向量大小和维度
- 其他NPU操作（卷积、池化等）
- 性能测试和基准测试
- 批量测试和自动化验证
