/*
 * Copyright (C) 2016-2023 C-SKY Microsystems Co., Ltd. All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the License); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "csi_nn.h"
#include "test_utils.h"

/**
 * NPU基础测试样例
 * 
 * 本测试样例演示如何编写NPU测试，包括：
 * 1. 会话配置和初始化
 * 2. 张量创建和数据准备
 * 3. 操作参数设置
 * 4. NPU图模式执行
 * 5. 结果验证
 * 6. 多种量化类型测试
 */

// NPU测试运行函数 - 加法操作
void npu_add_test_run(struct csinn_tensor *input0, struct csinn_tensor *input1,
                      struct csinn_tensor *output, struct csinn_diso_params *params,
                      struct csinn_session *sess, struct csinn_tensor *real_input0,
                      struct csinn_tensor *real_input1, float *reference_data, float threshold) {
    
    printf("  Initializing NPU session...\n");
    
    // 1. 初始化会话
    csinn_session_init(sess);
    csinn_set_input_number(2, sess);  // 设置输入数量
    csinn_set_output_number(1, sess); // 设置输出数量
    
    // 2. 初始化加法操作
    int ret = csinn_add_init(input0, input1, output, params);
    if (ret != CSINN_TRUE) {
        printf("  ✗ Add operation init failed\n");
        return;
    }
    printf("  ✓ Add operation initialized\n");
    
    // 3. 设置输入张量
    csinn_set_tensor_entry(input0, sess);
    csinn_set_tensor_entry(input1, sess);
    csinn_set_input(0, input0, sess);
    csinn_set_input(1, input1, sess);
    
    // 4. 执行加法操作
    ret = csinn_add(input0, input1, output, params);
    if (ret != CSINN_TRUE) {
        printf("  ✗ Add operation failed\n");
        return;
    }
    printf("  ✓ Add operation executed\n");
    
    // 5. 设置输出并建立会话
    csinn_set_output(0, output, sess);
    ret = csinn_session_setup(sess);
    if (ret != CSINN_TRUE) {
        printf("  ✗ Session setup failed\n");
        return;
    }
    printf("  ✓ Session setup completed\n");
    
    // 6. 更新输入数据并运行推理
    csinn_update_input(0, real_input0, sess);
    csinn_update_input(1, real_input1, sess);
    ret = csinn_session_run(sess);
    if (ret != CSINN_TRUE) {
        printf("  ✗ Session run failed\n");
        return;
    }
    printf("  ✓ NPU inference completed\n");
    
    // 7. 获取输出结果
    csinn_get_output(0, output, sess);
    
    // 8. 验证结果
    struct csinn_tensor *foutput = shl_ref_tensor_transform_f32(output);
    printf("  Verifying results...\n");
    result_verify_f32(reference_data, foutput->data, (float*)input0->data, 
                      threshold, csinn_tensor_size(output), false);
    
    // 9. 清理资源
    shl_ref_tensor_transform_free_f32(foutput);
    csinn_session_deinit(sess);
    csinn_free_session(sess);
}

// 测试INT8对称量化
void test_npu_add_i8_sym(struct csinn_tensor *input0, struct csinn_tensor *input1,
                         struct csinn_tensor *output, struct csinn_diso_params *params,
                         float *reference_data, float threshold) {
    printf("\n--- Testing INT8 Symmetric Quantization ---\n");
    
    struct csinn_session *sess = csinn_alloc_session();
    sess->base_api = CSINN_TH1520;                    // 使用TH1520 NPU
    sess->base_quant_type = CSINN_QUANT_INT8_SYM;     // INT8对称量化
    sess->base_run_mode = CSINN_RM_NPU_GRAPH;         // NPU图模式
    sess->debug_level = CSINN_DEBUG_LEVEL_INFO;       // 调试信息
    
    // 创建量化张量
    struct csinn_tensor *qinput0 = convert_f32_input(input0, CSINN_DTYPE_FLOAT32, sess);
    struct csinn_tensor *qinput1 = convert_f32_input(input1, CSINN_DTYPE_FLOAT32, sess);
    struct csinn_tensor *qoutput = convert_f32_input(output, CSINN_DTYPE_FLOAT32, sess);
    struct csinn_tensor *real_input0 = convert_f32_input(input0, CSINN_DTYPE_INT8, sess);
    struct csinn_tensor *real_input1 = convert_f32_input(input1, CSINN_DTYPE_INT8, sess);
    
    npu_add_test_run(qinput0, qinput1, qoutput, params, sess, real_input0, real_input1,
                     reference_data, threshold);
}

// 测试INT8非对称量化
void test_npu_add_i8_asym(struct csinn_tensor *input0, struct csinn_tensor *input1,
                          struct csinn_tensor *output, struct csinn_diso_params *params,
                          float *reference_data, float threshold) {
    printf("\n--- Testing INT8 Asymmetric Quantization ---\n");
    
    struct csinn_session *sess = csinn_alloc_session();
    sess->base_api = CSINN_TH1520;
    sess->base_quant_type = CSINN_QUANT_INT8_ASYM;     // INT8非对称量化
    sess->base_run_mode = CSINN_RM_NPU_GRAPH;
    sess->debug_level = CSINN_DEBUG_LEVEL_INFO;
    
    struct csinn_tensor *qinput0 = convert_f32_input(input0, CSINN_DTYPE_INT8, sess);
    struct csinn_tensor *qinput1 = convert_f32_input(input1, CSINN_DTYPE_INT8, sess);
    struct csinn_tensor *qoutput = convert_f32_input(output, CSINN_DTYPE_INT8, sess);
    struct csinn_tensor *real_input0 = convert_f32_input(input0, CSINN_DTYPE_INT8, sess);
    struct csinn_tensor *real_input1 = convert_f32_input(input1, CSINN_DTYPE_INT8, sess);
    
    npu_add_test_run(qinput0, qinput1, qoutput, params, sess, real_input0, real_input1,
                     reference_data, threshold);
}

// 测试UINT8非对称量化
void test_npu_add_u8_asym(struct csinn_tensor *input0, struct csinn_tensor *input1,
                          struct csinn_tensor *output, struct csinn_diso_params *params,
                          float *reference_data, float threshold) {
    printf("\n--- Testing UINT8 Asymmetric Quantization ---\n");
    
    struct csinn_session *sess = csinn_alloc_session();
    sess->base_api = CSINN_TH1520;
    sess->base_quant_type = CSINN_QUANT_UINT8_ASYM;    // UINT8非对称量化
    sess->base_run_mode = CSINN_RM_NPU_GRAPH;
    sess->debug_level = CSINN_DEBUG_LEVEL_INFO;
    
    struct csinn_tensor *qinput0 = convert_f32_input(input0, CSINN_DTYPE_UINT8, sess);
    struct csinn_tensor *qinput1 = convert_f32_input(input1, CSINN_DTYPE_UINT8, sess);
    struct csinn_tensor *qoutput = convert_f32_input(output, CSINN_DTYPE_UINT8, sess);
    struct csinn_tensor *real_input0 = convert_f32_input(input0, CSINN_DTYPE_UINT8, sess);
    struct csinn_tensor *real_input1 = convert_f32_input(input1, CSINN_DTYPE_UINT8, sess);
    
    npu_add_test_run(qinput0, qinput1, qoutput, params, sess, real_input0, real_input1,
                     reference_data, threshold);
}

// 主测试函数
void test_npu_add(struct csinn_tensor *input0, struct csinn_tensor *input1,
                  struct csinn_tensor *output, struct csinn_diso_params *params,
                  float *reference_data, float threshold) {
    printf("\n=== NPU Add Operation Test ===\n");
    
    // 设置操作参数
    params->base.api = CSINN_TH1520;
    params->base.layout = CSINN_LAYOUT_NCHW;
    params->base.name = "npu_add_test";
    
    // 测试不同的量化类型
    test_npu_add_i8_sym(input0, input1, output, params, reference_data, threshold);
    test_npu_add_i8_asym(input0, input1, output, params, reference_data, threshold);
    test_npu_add_u8_asym(input0, input1, output, params, reference_data, threshold);
}

// 计算参考结果
void compute_reference_add(float *input0, float *input1, float *output, int size) {
    for (int i = 0; i < size; i++) {
        output[i] = input0[i] + input1[i];
    }
}

// 初始化测试数据
void init_test_data(float *data, int size, float base_value, float range) {
    for (int i = 0; i < size; i++) {
        data[i] = base_value + ((float)(rand() % 1000) / 1000.0f - 0.5f) * range;
    }
}

int main(int argc, char **argv) {
    init_testsuite("NPU Basic Test Suite");
    
    // 测试配置
    int batch = 1, channel = 4, height = 8, width = 8;
    int size = batch * channel * height * width;
    float threshold = argc > 1 ? atof(argv[1]) : 0.95f;  // 余弦相似度阈值
    
    printf("Test Configuration:\n");
    printf("  Tensor shape: [%d, %d, %d, %d]\n", batch, channel, height, width);
    printf("  Total elements: %d\n", size);
    printf("  Similarity threshold: %.3f\n", threshold);
    printf("  NPU API: TH1520\n");
    printf("  Test operation: Element-wise Add\n");
    
    // 分配内存
    float *input0_data = (float*)malloc(size * sizeof(float));
    float *input1_data = (float*)malloc(size * sizeof(float));
    float *reference_data = (float*)malloc(size * sizeof(float));
    
    if (!input0_data || !input1_data || !reference_data) {
        printf("Memory allocation failed!\n");
        return -1;
    }
    
    // 初始化测试数据
    srand(42);  // 固定随机种子以确保结果可重现
    init_test_data(input0_data, size, 1.0f, 2.0f);   // 基值1.0，范围±1.0
    init_test_data(input1_data, size, 0.5f, 1.0f);   // 基值0.5，范围±0.5
    
    // 计算参考结果
    compute_reference_add(input0_data, input1_data, reference_data, size);
    
    printf("\nSample Data (first 8 elements):\n");
    printf("  Input0: ");
    for (int i = 0; i < 8; i++) printf("%.3f ", input0_data[i]);
    printf("\n  Input1: ");
    for (int i = 0; i < 8; i++) printf("%.3f ", input1_data[i]);
    printf("\n  Reference: ");
    for (int i = 0; i < 8; i++) printf("%.3f ", reference_data[i]);
    printf("\n");

    // 创建张量
    struct csinn_tensor *input0 = csinn_alloc_tensor(NULL);
    input0->dim[0] = batch;
    input0->dim[1] = channel;
    input0->dim[2] = height;
    input0->dim[3] = width;
    input0->dim_count = 4;
    input0->dtype = CSINN_DTYPE_FLOAT32;
    input0->layout = CSINN_LAYOUT_NCHW;
    input0->data = input0_data;
    input0->name = "input0";

    struct csinn_tensor *input1 = csinn_alloc_tensor(NULL);
    input1->dim[0] = batch;
    input1->dim[1] = channel;
    input1->dim[2] = height;
    input1->dim[3] = width;
    input1->dim_count = 4;
    input1->dtype = CSINN_DTYPE_FLOAT32;
    input1->layout = CSINN_LAYOUT_NCHW;
    input1->data = input1_data;
    input1->name = "input1";

    struct csinn_tensor *output = csinn_alloc_tensor(NULL);
    output->dim[0] = batch;
    output->dim[1] = channel;
    output->dim[2] = height;
    output->dim[3] = width;
    output->dim_count = 4;
    output->dtype = CSINN_DTYPE_FLOAT32;
    output->layout = CSINN_LAYOUT_NCHW;
    output->data = reference_data;
    output->name = "output";

    // 创建操作参数
    struct csinn_diso_params *params = csinn_alloc_params(sizeof(struct csinn_diso_params), NULL);

    // 运行NPU测试
    test_npu_add(input0, input1, output, params, reference_data, threshold);

    // 清理内存
    free(input0_data);
    free(input1_data);
    free(reference_data);

    printf("\n=== NPU Test Completed ===\n");
    return done_testing();
}
