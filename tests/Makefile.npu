# NPU测试样例Makefile
# 用于编译和运行NPU测试样例

CC = gcc
INCLUDE = -I../include -I./utils
CFLAGS = -O0 -g3 -mhard-float -Wall -Wextra
LDFLAGS = -L../lib/ -L../module/nna_ddk_install/x86/

# 库文件
LIBS = -lshl_th1520 -limgdnn_csim -lnnasession_csim -lpthread -lc -lm -lstdc++

# 测试目标
TEST_TARGETS = npu_basic_test npu_conv2d_test

# 工具对象文件
UTILS_OBJS = utils/test_utils.o

# 所有目标
all: $(TEST_TARGETS)

# 编译工具函数
utils/test_utils.o: utils/test_utils.c
	$(CC) -c $(CFLAGS) $(INCLUDE) $< -o $@

# 编译基础NPU测试
npu_basic_test: npu_basic_test.o $(UTILS_OBJS)
	$(CC) $< $(UTILS_OBJS) $(CFLAGS) $(LDFLAGS) $(LIBS) -o $@

npu_basic_test.o: npu_basic_test.c
	$(CC) -c $(CFLAGS) $(INCLUDE) $< -o $@

# 编译卷积NPU测试
npu_conv2d_test: npu_test_conv2d.o $(UTILS_OBJS)
	$(CC) $< $(UTILS_OBJS) $(CFLAGS) $(LDFLAGS) $(LIBS) -o $@

npu_test_conv2d.o: npu_test_conv2d.c
	$(CC) -c $(CFLAGS) $(INCLUDE) $< -o $@

# 运行测试
test: $(TEST_TARGETS)
	@echo "=== Running NPU Basic Test ==="
	./npu_basic_test 0.9
	@echo ""
	@echo "=== Running NPU Conv2D Test ==="
	./npu_conv2d_test 0.8

# 清理
clean:
	rm -rf $(TEST_TARGETS) *.o $(UTILS_OBJS) *.elf *.bin imgdnn_session_*

# 帮助信息
help:
	@echo "NPU Test Makefile"
	@echo ""
	@echo "Targets:"
	@echo "  all              - Build all test targets"
	@echo "  npu_basic_test   - Build basic NPU test"
	@echo "  npu_conv2d_test  - Build Conv2D NPU test"
	@echo "  test             - Run all tests"
	@echo "  clean            - Clean build files"
	@echo "  help             - Show this help"
	@echo ""
	@echo "Usage examples:"
	@echo "  make all                    # Build all tests"
	@echo "  make test                   # Run all tests"
	@echo "  ./npu_basic_test 0.95      # Run basic test with 0.95 threshold"
	@echo "  ./npu_conv2d_test 0.8      # Run conv2d test with 0.8 threshold"

.PHONY: all test clean help
