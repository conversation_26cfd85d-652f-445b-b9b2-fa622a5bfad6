/*
 * Copyright (C) 2016-2023 C-SKY Microsystems Co., Ltd. All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the License); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef NPU_TEST_CONFIG_H
#define NPU_TEST_CONFIG_H

/**
 * NPU测试配置文件
 * 
 * 本文件定义了NPU测试的各种配置参数和常量
 */

// NPU API配置
#define NPU_API                 CSINN_TH1520
#define NPU_RUN_MODE           CSINN_RM_NPU_GRAPH
#define NPU_LAYOUT             CSINN_LAYOUT_NCHW

// 量化类型配置
#define QUANT_INT8_SYM         CSINN_QUANT_INT8_SYM
#define QUANT_INT8_ASYM        CSINN_QUANT_INT8_ASYM
#define QUANT_UINT8_ASYM       CSINN_QUANT_UINT8_ASYM
#define QUANT_INT16_SYM        CSINN_QUANT_INT16_SYM

// 数据类型配置
#define DTYPE_FLOAT32          CSINN_DTYPE_FLOAT32
#define DTYPE_INT8             CSINN_DTYPE_INT8
#define DTYPE_UINT8            CSINN_DTYPE_UINT8
#define DTYPE_INT16            CSINN_DTYPE_INT16
#define DTYPE_INT32            CSINN_DTYPE_INT32

// 调试级别配置
#define DEBUG_LEVEL_NONE       CSINN_DEBUG_LEVEL_NONE
#define DEBUG_LEVEL_ERROR      CSINN_DEBUG_LEVEL_ERROR
#define DEBUG_LEVEL_WARNING    CSINN_DEBUG_LEVEL_WARNING
#define DEBUG_LEVEL_INFO       CSINN_DEBUG_LEVEL_INFO
#define DEBUG_LEVEL_DEBUG      CSINN_DEBUG_LEVEL_DEBUG

// 默认测试参数
#define DEFAULT_BATCH_SIZE     1
#define DEFAULT_CHANNEL_SIZE   4
#define DEFAULT_HEIGHT_SIZE    8
#define DEFAULT_WIDTH_SIZE     8

// 默认阈值配置
#define DEFAULT_BASIC_THRESHOLD    0.95f
#define DEFAULT_CONV2D_THRESHOLD   0.8f
#define DEFAULT_COMPLEX_THRESHOLD  0.7f

// 卷积参数配置
#define DEFAULT_KERNEL_SIZE    3
#define DEFAULT_STRIDE         1
#define DEFAULT_PADDING        1
#define DEFAULT_OUTPUT_CHANNELS 16

// 内存对齐配置
#define MEMORY_ALIGN_SIZE      64
#define TENSOR_ALIGN_SIZE      16

// 测试数据范围配置
#define TEST_DATA_MIN_VALUE    -10.0f
#define TEST_DATA_MAX_VALUE    10.0f
#define TEST_DATA_SCALE        1.0f

// 性能测试配置
#define PERF_TEST_ITERATIONS   100
#define WARMUP_ITERATIONS      10

// 错误码定义
#define NPU_TEST_SUCCESS       0
#define NPU_TEST_FAIL          -1
#define NPU_TEST_INIT_FAIL     -2
#define NPU_TEST_SETUP_FAIL    -3
#define NPU_TEST_RUN_FAIL      -4
#define NPU_TEST_VERIFY_FAIL   -5

// 测试类型定义
typedef enum {
    NPU_TEST_TYPE_BASIC = 0,
    NPU_TEST_TYPE_CONV2D,
    NPU_TEST_TYPE_POOLING,
    NPU_TEST_TYPE_ACTIVATION,
    NPU_TEST_TYPE_NORMALIZATION,
    NPU_TEST_TYPE_COMPLEX
} npu_test_type_t;

// 量化类型定义
typedef enum {
    NPU_QUANT_TYPE_I8_SYM = 0,
    NPU_QUANT_TYPE_I8_ASYM,
    NPU_QUANT_TYPE_U8_ASYM,
    NPU_QUANT_TYPE_I16_SYM
} npu_quant_type_t;

// 测试配置结构体
typedef struct {
    npu_test_type_t test_type;
    npu_quant_type_t quant_type;
    int batch_size;
    int channel_size;
    int height_size;
    int width_size;
    float threshold;
    bool enable_debug;
    bool enable_perf;
} npu_test_config_t;

// 默认配置初始化宏
#define NPU_TEST_CONFIG_INIT() { \
    .test_type = NPU_TEST_TYPE_BASIC, \
    .quant_type = NPU_QUANT_TYPE_I8_SYM, \
    .batch_size = DEFAULT_BATCH_SIZE, \
    .channel_size = DEFAULT_CHANNEL_SIZE, \
    .height_size = DEFAULT_HEIGHT_SIZE, \
    .width_size = DEFAULT_WIDTH_SIZE, \
    .threshold = DEFAULT_BASIC_THRESHOLD, \
    .enable_debug = false, \
    .enable_perf = false \
}

// 工具宏定义
#define NPU_TENSOR_SIZE(b, c, h, w) ((b) * (c) * (h) * (w))
#define NPU_CONV_OUTPUT_SIZE(input, kernel, stride, padding) \
    (((input) + 2 * (padding) - (kernel)) / (stride) + 1)

// 内存分配宏
#define NPU_MALLOC(size) malloc((size))
#define NPU_FREE(ptr) do { if (ptr) { free(ptr); (ptr) = NULL; } } while(0)

// 错误检查宏
#define NPU_CHECK_RET(ret, msg) do { \
    if ((ret) != CSINN_TRUE) { \
        printf("Error: %s (ret=%d)\n", (msg), (ret)); \
        return NPU_TEST_FAIL; \
    } \
} while(0)

#define NPU_CHECK_NULL(ptr, msg) do { \
    if ((ptr) == NULL) { \
        printf("Error: %s\n", (msg)); \
        return NPU_TEST_FAIL; \
    } \
} while(0)

// 调试输出宏
#ifdef NPU_DEBUG
#define NPU_DEBUG_PRINT(fmt, ...) printf("[DEBUG] " fmt "\n", ##__VA_ARGS__)
#else
#define NPU_DEBUG_PRINT(fmt, ...)
#endif

#define NPU_INFO_PRINT(fmt, ...) printf("[INFO] " fmt "\n", ##__VA_ARGS__)
#define NPU_ERROR_PRINT(fmt, ...) printf("[ERROR] " fmt "\n", ##__VA_ARGS__)

// 性能测量宏
#define NPU_PERF_START() clock_t _start_time = clock()
#define NPU_PERF_END(name) do { \
    clock_t _end_time = clock(); \
    double _time_spent = ((double)(_end_time - _start_time)) / CLOCKS_PER_SEC; \
    printf("[PERF] %s: %.3f ms\n", (name), _time_spent * 1000); \
} while(0)

// 版本信息
#define NPU_TEST_VERSION_MAJOR 1
#define NPU_TEST_VERSION_MINOR 0
#define NPU_TEST_VERSION_PATCH 0
#define NPU_TEST_VERSION_STRING "1.0.0"

#endif // NPU_TEST_CONFIG_H
