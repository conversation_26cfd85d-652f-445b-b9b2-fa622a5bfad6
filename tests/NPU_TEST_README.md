# NPU测试样例使用指南

本文档介绍如何编写和运行NPU测试样例，用于验证TH1520 NPU的功能和性能。

## 目录结构

```
tests/
├── npu_basic_test.c        # 基础NPU测试样例（加法操作）
├── npu_test_conv2d.c       # 卷积NPU测试样例
├── Makefile.npu           # NPU测试编译文件
├── NPU_TEST_README.md     # 本文档
└── utils/
    └── test_utils.c       # 测试工具函数
```

## 测试样例说明

### 1. 基础NPU测试 (npu_basic_test.c)

**功能**: 测试NPU的基本加法操作，支持多种量化类型

**特点**:
- 支持INT8对称量化、INT8非对称量化、UINT8非对称量化
- 完整的NPU图模式执行流程
- 详细的测试步骤输出和结果验证
- 可配置的相似度阈值

**测试流程**:
1. 会话配置和初始化
2. 张量创建和数据准备
3. 操作参数设置
4. NPU图模式执行
5. 结果验证和清理

### 2. 卷积NPU测试 (npu_test_conv2d.c)

**功能**: 测试NPU的卷积操作

**特点**:
- 支持2D卷积操作
- 多种量化类型支持
- 可配置的卷积参数（步长、填充等）
- 参考结果计算和验证

## 编译和运行

### 编译测试

```bash
# 编译所有NPU测试
make -f Makefile.npu all

# 编译特定测试
make -f Makefile.npu npu_basic_test
make -f Makefile.npu npu_conv2d_test
```

### 运行测试

```bash
# 运行所有测试
make -f Makefile.npu test

# 运行基础测试（默认阈值0.95）
./npu_basic_test

# 运行基础测试（自定义阈值）
./npu_basic_test 0.9

# 运行卷积测试
./npu_conv2d_test 0.8
```

### 清理编译文件

```bash
make -f Makefile.npu clean
```

## NPU测试编写指南

### 1. 基本结构

每个NPU测试应包含以下基本结构：

```c
#include "csi_nn.h"
#include "test_utils.h"

// NPU测试运行函数
void npu_test_run(/* 参数 */) {
    // 1. 初始化会话
    csinn_session_init(sess);
    csinn_set_input_number(input_num, sess);
    csinn_set_output_number(output_num, sess);
    
    // 2. 初始化操作
    csinn_xxx_init(input, output, params);
    
    // 3. 设置输入输出
    csinn_set_tensor_entry(input, sess);
    csinn_set_input(0, input, sess);
    
    // 4. 执行操作
    csinn_xxx(input, output, params);
    
    // 5. 建立会话并运行
    csinn_set_output(0, output, sess);
    csinn_session_setup(sess);
    csinn_update_input(0, real_input, sess);
    csinn_session_run(sess);
    
    // 6. 获取结果并验证
    csinn_get_output(0, output, sess);
    // 验证结果...
    
    // 7. 清理资源
    csinn_session_deinit(sess);
    csinn_free_session(sess);
}
```

### 2. 会话配置

```c
struct csinn_session *sess = csinn_alloc_session();
sess->base_api = CSINN_TH1520;                    // 使用TH1520 NPU
sess->base_quant_type = CSINN_QUANT_INT8_SYM;     // 量化类型
sess->base_run_mode = CSINN_RM_NPU_GRAPH;         // NPU图模式
sess->debug_level = CSINN_DEBUG_LEVEL_INFO;       // 调试级别
```

### 3. 量化类型支持

- `CSINN_QUANT_INT8_SYM`: INT8对称量化
- `CSINN_QUANT_INT8_ASYM`: INT8非对称量化
- `CSINN_QUANT_UINT8_ASYM`: UINT8非对称量化
- `CSINN_QUANT_INT16_SYM`: INT16对称量化

### 4. 张量创建

```c
struct csinn_tensor *input = csinn_alloc_tensor(sess);
input->dim[0] = batch;
input->dim[1] = channel;
input->dim[2] = height;
input->dim[3] = width;
input->dim_count = 4;
input->dtype = CSINN_DTYPE_FLOAT32;
input->layout = CSINN_LAYOUT_NCHW;
input->data = input_data;
input->name = "input";
```

### 5. 结果验证

```c
// 转换输出为float32
struct csinn_tensor *foutput = shl_ref_tensor_transform_f32(output);

// 验证结果（使用余弦相似度）
result_verify_f32(reference_data, foutput->data, input_data, 
                  threshold, size, false);

// 清理
shl_ref_tensor_transform_free_f32(foutput);
```

## 测试参数说明

### 相似度阈值

- **范围**: 0.0 - 1.0
- **推荐值**: 
  - 基础操作: 0.95+
  - 复杂操作: 0.8+
- **说明**: 使用余弦相似度衡量NPU输出与参考结果的相似程度

### 张量形状

- **NCHW格式**: [batch, channel, height, width]
- **建议大小**: 根据NPU内存限制调整
- **对齐要求**: 某些操作可能需要特定的对齐

## 常见问题

### 1. 编译错误

**问题**: 找不到头文件或库文件
**解决**: 检查INCLUDE和LDFLAGS路径是否正确

### 2. 运行时错误

**问题**: NPU初始化失败
**解决**: 
- 检查NPU驱动是否正确安装
- 确认TH1520硬件支持
- 检查权限设置

### 3. 精度问题

**问题**: 测试结果精度不符合预期
**解决**:
- 调整相似度阈值
- 检查量化参数设置
- 验证输入数据范围

### 4. 内存问题

**问题**: 内存分配失败
**解决**:
- 减小张量大小
- 检查系统内存使用情况
- 确保正确释放内存

## 扩展测试

### 添加新操作测试

1. 参考现有测试样例结构
2. 实现操作特定的测试函数
3. 添加多种量化类型支持
4. 更新Makefile编译规则
5. 编写测试文档

### 性能测试

可以在测试中添加时间测量：

```c
#include <time.h>

clock_t start = clock();
csinn_session_run(sess);
clock_t end = clock();

double time_spent = ((double)(end - start)) / CLOCKS_PER_SEC;
printf("NPU inference time: %.3f ms\n", time_spent * 1000);
```

## 参考资料

- CSI-NN2 API文档
- TH1520 NPU用户手册
- 量化技术指南
- 测试最佳实践
