/*
 * Copyright (C) 2016-2023 C-SKY Microsystems Co., Ltd. All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the License); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "csi_nn.h"
#include "test_utils.h"

// NPU卷积测试运行函数
void op_test_run_conv2d(struct csinn_tensor *input, struct csinn_tensor *kernel, 
                        struct csinn_tensor *bias, struct csinn_tensor *output,
                        struct csinn_conv2d_params *params, struct csinn_session *sess,
                        struct csinn_tensor *real_input, float *output_data, float diff) {
    
    printf("Running NPU Conv2D test...\n");
    
    // 初始化会话
    csinn_session_init(sess);
    csinn_set_input_number(1, sess);
    csinn_set_output_number(1, sess);
    
    // 初始化卷积操作
    csinn_conv2d_init(input, output, kernel, bias, params);
    
    // 设置输入输出张量
    csinn_set_tensor_entry(input, sess);
    csinn_set_input(0, input, sess);
    
    // 执行卷积操作
    csinn_conv2d(input, output, kernel, bias, params);
    
    // 设置输出并建立会话
    csinn_set_output(0, output, sess);
    csinn_session_setup(sess);
    
    // 更新输入数据并运行
    csinn_update_input(0, real_input, sess);
    csinn_session_run(sess);
    
    // 获取输出结果
    csinn_get_output(0, output, sess);
    
    // 转换输出为float32进行验证
    struct csinn_tensor *foutput = shl_ref_tensor_transform_f32(output);
    result_verify_f32(output_data, foutput->data, (float*)input->data, diff, 
                      csinn_tensor_size(output), false);
    
    // 清理资源
    shl_ref_tensor_transform_free_f32(foutput);
    csinn_session_deinit(sess);
    csinn_free_session(sess);
}

// 测试i8对称量化
void test_conv2d_i8_sym(struct csinn_tensor *input, struct csinn_tensor *kernel,
                        struct csinn_tensor *bias, struct csinn_tensor *output,
                        struct csinn_conv2d_params *params, float difference) {
    printf("Testing Conv2D i8 symmetric quantization\n");
    struct csinn_session *sess = csinn_alloc_session();
    sess->base_api = CSINN_TH1520;
    sess->base_quant_type = CSINN_QUANT_INT8_SYM;
    sess->base_run_mode = CSINN_RM_NPU_GRAPH;
    
    struct csinn_tensor *qinput = convert_f32_input(input, CSINN_DTYPE_FLOAT32, sess);
    struct csinn_tensor *qkernel = convert_f32_input(kernel, CSINN_DTYPE_FLOAT32, sess);
    struct csinn_tensor *qbias = convert_f32_input(bias, CSINN_DTYPE_FLOAT32, sess);
    struct csinn_tensor *qoutput = convert_f32_input(output, CSINN_DTYPE_FLOAT32, sess);
    struct csinn_tensor *real_input = convert_f32_input(input, CSINN_DTYPE_INT8, sess);
    
    op_test_run_conv2d(qinput, qkernel, qbias, qoutput, params, sess, real_input,
                       (float*)output->data, difference);
}

// 测试i8非对称量化
void test_conv2d_i8_asym(struct csinn_tensor *input, struct csinn_tensor *kernel,
                         struct csinn_tensor *bias, struct csinn_tensor *output,
                         struct csinn_conv2d_params *params, float difference) {
    printf("Testing Conv2D i8 asymmetric quantization\n");
    struct csinn_session *sess = csinn_alloc_session();
    sess->base_api = CSINN_TH1520;
    sess->base_quant_type = CSINN_QUANT_INT8_ASYM;
    sess->base_run_mode = CSINN_RM_NPU_GRAPH;
    
    struct csinn_tensor *qinput = convert_f32_input(input, CSINN_DTYPE_INT8, sess);
    struct csinn_tensor *qkernel = convert_f32_input(kernel, CSINN_DTYPE_INT8, sess);
    struct csinn_tensor *qbias = convert_f32_input(bias, CSINN_DTYPE_INT32, sess);
    struct csinn_tensor *qoutput = convert_f32_input(output, CSINN_DTYPE_INT8, sess);
    struct csinn_tensor *real_input = convert_f32_input(input, CSINN_DTYPE_INT8, sess);
    
    op_test_run_conv2d(qinput, qkernel, qbias, qoutput, params, sess, real_input,
                       (float*)output->data, difference);
}

// 测试u8非对称量化
void test_conv2d_u8_asym(struct csinn_tensor *input, struct csinn_tensor *kernel,
                         struct csinn_tensor *bias, struct csinn_tensor *output,
                         struct csinn_conv2d_params *params, float difference) {
    printf("Testing Conv2D u8 asymmetric quantization\n");
    struct csinn_session *sess = csinn_alloc_session();
    sess->base_api = CSINN_TH1520;
    sess->base_quant_type = CSINN_QUANT_UINT8_ASYM;
    sess->base_run_mode = CSINN_RM_NPU_GRAPH;
    
    struct csinn_tensor *qinput = convert_f32_input(input, CSINN_DTYPE_UINT8, sess);
    struct csinn_tensor *qkernel = convert_f32_input(kernel, CSINN_DTYPE_UINT8, sess);
    struct csinn_tensor *qbias = convert_f32_input(bias, CSINN_DTYPE_INT32, sess);
    struct csinn_tensor *qoutput = convert_f32_input(output, CSINN_DTYPE_UINT8, sess);
    struct csinn_tensor *real_input = convert_f32_input(input, CSINN_DTYPE_UINT8, sess);
    
    op_test_run_conv2d(qinput, qkernel, qbias, qoutput, params, sess, real_input,
                       (float*)output->data, difference);
}

// 主测试函数
void test_conv2d(struct csinn_tensor *input, struct csinn_tensor *kernel,
                 struct csinn_tensor *bias, struct csinn_tensor *output,
                 struct csinn_conv2d_params *params, float difference) {
    printf("=== Testing NPU Conv2D Operation ===\n");
    params->base.api = CSINN_TH1520;
    params->base.layout = CSINN_LAYOUT_NCHW;
    
    // 测试不同的量化类型
    test_conv2d_i8_sym(input, kernel, bias, output, params, difference);
    test_conv2d_i8_asym(input, kernel, bias, output, params, difference);
    test_conv2d_u8_asym(input, kernel, bias, output, params, difference);
}

// 创建测试数据
void create_conv_test_data(float *data, int size, float scale) {
    for (int i = 0; i < size; i++) {
        data[i] = ((float)(rand() % 200) - 100.0f) / 100.0f * scale;
    }
}

// 计算卷积参考结果（简化版本）
void compute_reference_conv2d(float *input, float *kernel, float *bias, float *output,
                              int batch, int in_c, int in_h, int in_w,
                              int out_c, int k_h, int k_w,
                              int stride_h, int stride_w,
                              int pad_h, int pad_w) {
    int out_h = (in_h + 2 * pad_h - k_h) / stride_h + 1;
    int out_w = (in_w + 2 * pad_w - k_w) / stride_w + 1;
    
    // 简化的卷积计算（仅用于演示）
    for (int b = 0; b < batch; b++) {
        for (int oc = 0; oc < out_c; oc++) {
            for (int oh = 0; oh < out_h; oh++) {
                for (int ow = 0; ow < out_w; ow++) {
                    float sum = bias[oc];
                    for (int ic = 0; ic < in_c; ic++) {
                        for (int kh = 0; kh < k_h; kh++) {
                            for (int kw = 0; kw < k_w; kw++) {
                                int ih = oh * stride_h - pad_h + kh;
                                int iw = ow * stride_w - pad_w + kw;
                                if (ih >= 0 && ih < in_h && iw >= 0 && iw < in_w) {
                                    int input_idx = b * in_c * in_h * in_w + ic * in_h * in_w + ih * in_w + iw;
                                    int kernel_idx = oc * in_c * k_h * k_w + ic * k_h * k_w + kh * k_w + kw;
                                    sum += input[input_idx] * kernel[kernel_idx];
                                }
                            }
                        }
                    }
                    int output_idx = b * out_c * out_h * out_w + oc * out_h * out_w + oh * out_w + ow;
                    output[output_idx] = sum;
                }
            }
        }
    }
}

int main(int argc, char **argv) {
    init_testsuite("NPU Conv2D Test Suite");
    
    // 测试参数
    int batch = 1, in_c = 3, in_h = 8, in_w = 8;
    int out_c = 16, k_h = 3, k_w = 3;
    int stride_h = 1, stride_w = 1;
    int pad_h = 1, pad_w = 1;
    
    int out_h = (in_h + 2 * pad_h - k_h) / stride_h + 1;
    int out_w = (in_w + 2 * pad_w - k_w) / stride_w + 1;
    
    int input_size = batch * in_c * in_h * in_w;
    int kernel_size = out_c * in_c * k_h * k_w;
    int bias_size = out_c;
    int output_size = batch * out_c * out_h * out_w;
    
    float difference = argc > 1 ? atof(argv[1]) : 0.9f;
    
    printf("Test configuration:\n");
    printf("  Input shape: [%d, %d, %d, %d]\n", batch, in_c, in_h, in_w);
    printf("  Kernel shape: [%d, %d, %d, %d]\n", out_c, in_c, k_h, k_w);
    printf("  Output shape: [%d, %d, %d, %d]\n", batch, out_c, out_h, out_w);
    printf("  Stride: [%d, %d], Padding: [%d, %d]\n", stride_h, stride_w, pad_h, pad_w);
    printf("  Similarity threshold: %.3f\n", difference);
    
    return done_testing();
}
