/*
 * NPU向量点乘测试样例
 * 使用TH1520 NPU实现向量点乘操作
 */

#include "csinn/csi_nn.h"
#include "test_utils.h"

// NPU向量点乘测试运行函数
void npu_dot_product_test_run(struct csinn_tensor *input0, struct csinn_tensor *input1,
                              struct csinn_tensor *output, struct csinn_diso_params *params,
                              struct csinn_session *sess, struct csinn_tensor *real_input0,
                              struct csinn_tensor *real_input1, float *reference_data, float threshold) {
    
    printf("Running NPU dot product test...\n");
    
    // 1. 初始化会话
    csinn_session_init(sess);
    csinn_set_input_number(2, sess);
    csinn_set_output_number(1, sess);
    
    // 2. 初始化乘法操作
    int ret = csinn_mul_init(input0, input1, output, params);
    if (ret != CSINN_TRUE) {
        printf("Mul operation init failed\n");
        return;
    }
    
    // 3. 设置输入张量
    csinn_set_tensor_entry(input0, sess);
    csinn_set_tensor_entry(input1, sess);
    csinn_set_input(0, input0, sess);
    csinn_set_input(1, input1, sess);
    
    // 4. 执行乘法操作
    ret = csinn_mul(input0, input1, output, params);
    if (ret != CSINN_TRUE) {
        printf("Mul operation failed\n");
        return;
    }
    
    // 5. 设置输出并建立会话
    csinn_set_output(0, output, sess);
    ret = csinn_session_setup(sess);
    if (ret != CSINN_TRUE) {
        printf("Session setup failed\n");
        return;
    }
    
    // 6. 更新输入数据并运行
    csinn_update_input(0, real_input0, sess);
    csinn_update_input(1, real_input1, sess);
    ret = csinn_session_run(sess);
    if (ret != CSINN_TRUE) {
        printf("Session run failed\n");
        return;
    }
    
    // 7. 获取输出结果
    csinn_get_output(0, output, sess);
    
    // 8. 验证结果
    struct csinn_tensor *foutput = shl_ref_tensor_transform_f32(output);
    result_verify_f32(reference_data, foutput->data, (float*)input0->data, 
                      threshold, csinn_tensor_size(output), false);
    
    // 9. 清理资源
    shl_ref_tensor_transform_free_f32(foutput);
    csinn_session_deinit(sess);
    csinn_free_session(sess);
}

// 测试INT8对称量化
void test_dot_product_i8_sym(struct csinn_tensor *input0, struct csinn_tensor *input1,
                             struct csinn_tensor *output, struct csinn_diso_params *params,
                             float *reference_data, float threshold) {
    printf("Testing dot product with INT8 symmetric quantization\n");
    
    struct csinn_session *sess = csinn_alloc_session();
    sess->base_api = CSINN_TH1520;
    sess->base_quant_type = CSINN_QUANT_INT8_SYM;
    sess->base_run_mode = CSINN_RM_NPU_GRAPH;
    
    struct csinn_tensor *qinput0 = convert_f32_input(input0, CSINN_DTYPE_FLOAT32, sess);
    struct csinn_tensor *qinput1 = convert_f32_input(input1, CSINN_DTYPE_FLOAT32, sess);
    struct csinn_tensor *qoutput = convert_f32_input(output, CSINN_DTYPE_FLOAT32, sess);
    struct csinn_tensor *real_input0 = convert_f32_input(input0, CSINN_DTYPE_INT8, sess);
    struct csinn_tensor *real_input1 = convert_f32_input(input1, CSINN_DTYPE_INT8, sess);
    
    npu_dot_product_test_run(qinput0, qinput1, qoutput, params, sess, real_input0, real_input1,
                            reference_data, threshold);
}

// 测试INT8非对称量化
void test_dot_product_i8_asym(struct csinn_tensor *input0, struct csinn_tensor *input1,
                              struct csinn_tensor *output, struct csinn_diso_params *params,
                              float *reference_data, float threshold) {
    printf("Testing dot product with INT8 asymmetric quantization\n");
    
    struct csinn_session *sess = csinn_alloc_session();
    sess->base_api = CSINN_TH1520;
    sess->base_quant_type = CSINN_QUANT_INT8_ASYM;
    sess->base_run_mode = CSINN_RM_NPU_GRAPH;
    
    struct csinn_tensor *qinput0 = convert_f32_input(input0, CSINN_DTYPE_INT8, sess);
    struct csinn_tensor *qinput1 = convert_f32_input(input1, CSINN_DTYPE_INT8, sess);
    struct csinn_tensor *qoutput = convert_f32_input(output, CSINN_DTYPE_INT8, sess);
    struct csinn_tensor *real_input0 = convert_f32_input(input0, CSINN_DTYPE_INT8, sess);
    struct csinn_tensor *real_input1 = convert_f32_input(input1, CSINN_DTYPE_INT8, sess);
    
    npu_dot_product_test_run(qinput0, qinput1, qoutput, params, sess, real_input0, real_input1,
                            reference_data, threshold);
}

// 主测试函数
void test_npu_dot_product(struct csinn_tensor *input0, struct csinn_tensor *input1,
                          struct csinn_tensor *output, struct csinn_diso_params *params,
                          float *reference_data, float threshold) {
    printf("=== NPU Dot Product Test ===\n");
    
    params->base.api = CSINN_TH1520;
    params->base.layout = CSINN_LAYOUT_N;
    params->base.name = "npu_dot_product";
    
    // 测试不同的量化类型
    test_dot_product_i8_sym(input0, input1, output, params, reference_data, threshold);
    test_dot_product_i8_asym(input0, input1, output, params, reference_data, threshold);
}

// 计算向量点乘参考结果
void compute_dot_product_reference(float *input0, float *input1, float *output, int size) {
    for (int i = 0; i < size; i++) {
        output[i] = input0[i] * input1[i];
    }
}

// 初始化测试向量数据
void init_vector_data(float *data, int size, float scale) {
    for (int i = 0; i < size; i++) {
        data[i] = ((float)(i + 1)) * scale;
    }
}

int main(int argc, char **argv) {
    init_testsuite("NPU Vector Dot Product Test");
    
    // 测试配置 - 使用1D向量
    int vector_size = 1024;  // 向量长度
    float threshold = argc > 1 ? atof(argv[1]) : 0.95f;
    
    printf("Test Configuration:\n");
    printf("  Vector size: %d\n", vector_size);
    printf("  Similarity threshold: %.3f\n", threshold);
    printf("  NPU API: TH1520\n");
    printf("  Operation: Element-wise multiplication (dot product)\n");
    
    // 分配内存
    float *input0_data = (float*)malloc(vector_size * sizeof(float));
    float *input1_data = (float*)malloc(vector_size * sizeof(float));
    float *reference_data = (float*)malloc(vector_size * sizeof(float));
    
    if (!input0_data || !input1_data || !reference_data) {
        printf("Memory allocation failed!\n");
        return -1;
    }
    
    // 初始化测试数据
    init_vector_data(input0_data, vector_size, 0.1f);
    init_vector_data(input1_data, vector_size, 0.2f);
    
    // 计算参考结果
    compute_dot_product_reference(input0_data, input1_data, reference_data, vector_size);
    
    printf("\nSample Data (first 8 elements):\n");
    printf("  Vector A: ");
    for (int i = 0; i < 8; i++) printf("%.3f ", input0_data[i]);
    printf("\n  Vector B: ");
    for (int i = 0; i < 8; i++) printf("%.3f ", input1_data[i]);
    printf("\n  Reference: ");
    for (int i = 0; i < 8; i++) printf("%.3f ", reference_data[i]);
    printf("\n");
    
    // 创建1D张量
    struct csinn_tensor *input0 = csinn_alloc_tensor(NULL);
    input0->dim[0] = vector_size;
    input0->dim_count = 1;
    input0->dtype = CSINN_DTYPE_FLOAT32;
    input0->layout = CSINN_LAYOUT_N;
    input0->data = input0_data;
    input0->name = "vector_a";
    
    struct csinn_tensor *input1 = csinn_alloc_tensor(NULL);
    input1->dim[0] = vector_size;
    input1->dim_count = 1;
    input1->dtype = CSINN_DTYPE_FLOAT32;
    input1->layout = CSINN_LAYOUT_N;
    input1->data = input1_data;
    input1->name = "vector_b";
    
    struct csinn_tensor *output = csinn_alloc_tensor(NULL);
    output->dim[0] = vector_size;
    output->dim_count = 1;
    output->dtype = CSINN_DTYPE_FLOAT32;
    output->layout = CSINN_LAYOUT_N;
    output->data = reference_data;
    output->name = "dot_product_result";
    
    // 创建操作参数
    struct csinn_diso_params *params = csinn_alloc_params(sizeof(struct csinn_diso_params), NULL);
    
    // 运行NPU测试
    test_npu_dot_product(input0, input1, output, params, reference_data, threshold);
    
    // 清理内存
    free(input0_data);
    free(input1_data);
    free(reference_data);
    
    printf("\n=== NPU Dot Product Test Completed ===\n");
    return done_testing();
}
