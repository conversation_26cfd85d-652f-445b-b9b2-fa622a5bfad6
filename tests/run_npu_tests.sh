#!/bin/bash

# NPU测试运行脚本
# 用于自动化运行NPU测试样例

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    print_info "Checking dependencies..."
    
    # 检查编译器
    if ! command -v gcc &> /dev/null; then
        print_error "GCC compiler not found"
        exit 1
    fi
    
    # 检查make
    if ! command -v make &> /dev/null; then
        print_error "Make not found"
        exit 1
    fi
    
    # 检查必要的头文件和库文件
    if [ ! -f "../include/csinn/csi_nn.h" ]; then
        print_warning "CSI-NN2 header files not found in expected location"
    fi
    
    print_success "Dependencies check completed"
}

# 编译测试
build_tests() {
    print_info "Building NPU tests..."
    
    if make -f Makefile.npu clean > /dev/null 2>&1; then
        print_info "Cleaned previous build files"
    fi
    
    if make -f Makefile.npu all; then
        print_success "NPU tests built successfully"
    else
        print_error "Failed to build NPU tests"
        exit 1
    fi
}

# 运行基础测试
run_basic_test() {
    local threshold=${1:-0.95}
    print_info "Running NPU basic test with threshold $threshold..."
    
    if [ ! -f "./npu_basic_test" ]; then
        print_error "npu_basic_test executable not found"
        return 1
    fi
    
    echo "----------------------------------------"
    if ./npu_basic_test $threshold; then
        print_success "NPU basic test passed"
        return 0
    else
        print_error "NPU basic test failed"
        return 1
    fi
}

# 运行卷积测试
run_conv2d_test() {
    local threshold=${1:-0.8}
    print_info "Running NPU Conv2D test with threshold $threshold..."
    
    if [ ! -f "./npu_conv2d_test" ]; then
        print_error "npu_conv2d_test executable not found"
        return 1
    fi
    
    echo "----------------------------------------"
    if ./npu_conv2d_test $threshold; then
        print_success "NPU Conv2D test passed"
        return 0
    else
        print_error "NPU Conv2D test failed"
        return 1
    fi
}

# 运行所有测试
run_all_tests() {
    local basic_threshold=${1:-0.95}
    local conv2d_threshold=${2:-0.8}
    
    print_info "Running all NPU tests..."
    
    local passed=0
    local total=2
    
    # 运行基础测试
    if run_basic_test $basic_threshold; then
        ((passed++))
    fi
    
    echo ""
    
    # 运行卷积测试
    if run_conv2d_test $conv2d_threshold; then
        ((passed++))
    fi
    
    echo ""
    echo "========================================"
    print_info "Test Summary: $passed/$total tests passed"
    
    if [ $passed -eq $total ]; then
        print_success "All NPU tests passed!"
        return 0
    else
        print_error "Some NPU tests failed"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "NPU Test Runner Script"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  build                    Build all NPU tests"
    echo "  basic [threshold]        Run basic NPU test (default threshold: 0.95)"
    echo "  conv2d [threshold]       Run Conv2D NPU test (default threshold: 0.8)"
    echo "  all [basic_th] [conv_th] Run all tests with optional thresholds"
    echo "  clean                    Clean build files"
    echo "  help                     Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 build                 # Build all tests"
    echo "  $0 basic                 # Run basic test with default threshold"
    echo "  $0 basic 0.9             # Run basic test with threshold 0.9"
    echo "  $0 all 0.95 0.8          # Run all tests with custom thresholds"
    echo "  $0 clean                 # Clean build files"
    echo ""
    echo "Thresholds:"
    echo "  - Range: 0.0 to 1.0 (cosine similarity)"
    echo "  - Higher values = stricter requirements"
    echo "  - Recommended: basic >= 0.9, conv2d >= 0.8"
}

# 清理构建文件
clean_build() {
    print_info "Cleaning build files..."
    if make -f Makefile.npu clean; then
        print_success "Build files cleaned"
    else
        print_error "Failed to clean build files"
        exit 1
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "         NPU Test Runner"
    echo "========================================"
    echo ""
    
    # 检查是否在正确的目录
    if [ ! -f "Makefile.npu" ]; then
        print_error "Makefile.npu not found. Please run this script from the tests directory."
        exit 1
    fi
    
    case "${1:-all}" in
        "build")
            check_dependencies
            build_tests
            ;;
        "basic")
            check_dependencies
            build_tests
            run_basic_test $2
            ;;
        "conv2d")
            check_dependencies
            build_tests
            run_conv2d_test $2
            ;;
        "all")
            check_dependencies
            build_tests
            run_all_tests $2 $3
            ;;
        "clean")
            clean_build
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "Unknown command: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
