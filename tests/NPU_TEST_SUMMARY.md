# NPU测试样例总结

根据参考文档，我已经为您创建了一套完整的NPU测试样例，用于测试TH1520 NPU的功能和性能。

## 创建的文件列表

### 1. 核心测试文件

| 文件名 | 描述 | 功能 |
|--------|------|------|
| `npu_basic_test.c` | 基础NPU测试样例 | 测试NPU加法操作，支持多种量化类型 |
| `npu_test_conv2d.c` | 卷积NPU测试样例 | 测试NPU 2D卷积操作 |
| `include/test.c` | 改进的测试样例 | 原有测试文件的增强版本 |

### 2. 构建和运行工具

| 文件名 | 描述 | 功能 |
|--------|------|------|
| `Makefile.npu` | NPU测试编译文件 | 自动化编译NPU测试样例 |
| `run_npu_tests.sh` | 测试运行脚本 | 自动化运行和管理NPU测试 |

### 3. 配置和文档

| 文件名 | 描述 | 功能 |
|--------|------|------|
| `npu_test_config.h` | 测试配置头文件 | 定义测试参数和常量 |
| `NPU_TEST_README.md` | 详细使用指南 | 完整的测试编写和使用说明 |
| `NPU_TEST_SUMMARY.md` | 本总结文档 | 项目概览和快速开始指南 |

## 主要特性

### ✅ 完整的NPU测试框架
- 支持TH1520 NPU API
- NPU图模式执行
- 多种量化类型支持
- 详细的错误处理和调试信息

### ✅ 多种量化类型支持
- INT8对称量化 (CSINN_QUANT_INT8_SYM)
- INT8非对称量化 (CSINN_QUANT_INT8_ASYM)  
- UINT8非对称量化 (CSINN_QUANT_UINT8_ASYM)
- INT16对称量化 (CSINN_QUANT_INT16_SYM)

### ✅ 丰富的测试操作
- 基础算术操作（加法、乘法）
- 卷积操作（Conv2D）
- 可扩展到其他NPU操作

### ✅ 结果验证机制
- 余弦相似度验证
- 可配置的精度阈值
- 详细的错误报告

### ✅ 自动化工具
- 一键编译和运行
- 批量测试执行
- 清理和管理功能

## 快速开始

### 1. 编译测试

```bash
cd tests
make -f Makefile.npu all
```

### 2. 运行基础测试

```bash
# 使用默认阈值
./npu_basic_test

# 使用自定义阈值
./npu_basic_test 0.9
```

### 3. 运行所有测试

```bash
# 使用脚本运行所有测试
./run_npu_tests.sh all

# 或使用make
make -f Makefile.npu test
```

### 4. 查看帮助

```bash
./run_npu_tests.sh help
make -f Makefile.npu help
```

## 测试流程说明

### NPU测试的标准流程

1. **会话初始化**
   ```c
   struct csinn_session *sess = csinn_alloc_session();
   sess->base_api = CSINN_TH1520;
   sess->base_run_mode = CSINN_RM_NPU_GRAPH;
   csinn_session_init(sess);
   ```

2. **张量创建和配置**
   ```c
   struct csinn_tensor *input = csinn_alloc_tensor(sess);
   // 配置张量维度、数据类型、布局等
   ```

3. **操作初始化和执行**
   ```c
   csinn_xxx_init(input, output, params);
   csinn_xxx(input, output, params);
   ```

4. **NPU推理执行**
   ```c
   csinn_session_setup(sess);
   csinn_session_run(sess);
   ```

5. **结果验证**
   ```c
   csinn_get_output(0, output, sess);
   result_verify_f32(reference, output_data, input_data, threshold, size, false);
   ```

## 配置参数

### 默认测试配置

- **张量形状**: [1, 4, 8, 8] (NCHW格式)
- **数据类型**: FLOAT32
- **量化类型**: INT8_SYM, INT8_ASYM, UINT8_ASYM
- **相似度阈值**: 基础操作 0.95, 卷积操作 0.8

### 可调整参数

- 张量维度和大小
- 量化类型和精度
- 相似度验证阈值
- 调试输出级别

## 扩展指南

### 添加新的NPU操作测试

1. 参考 `npu_basic_test.c` 的结构
2. 实现操作特定的测试函数
3. 添加多种量化类型支持
4. 更新Makefile编译规则
5. 添加到测试脚本中

### 性能测试集成

可以在测试中添加性能测量：

```c
#include <time.h>

NPU_PERF_START();
csinn_session_run(sess);
NPU_PERF_END("NPU Inference");
```

## 故障排除

### 常见问题

1. **编译错误**: 检查头文件和库文件路径
2. **NPU初始化失败**: 确认硬件支持和驱动安装
3. **精度问题**: 调整相似度阈值或检查量化参数
4. **内存问题**: 减小张量大小或检查内存使用

### 调试技巧

- 启用调试输出: `sess->debug_level = CSINN_DEBUG_LEVEL_INFO`
- 使用配置文件中的调试宏
- 检查每个步骤的返回值

## 下一步建议

1. **扩展测试覆盖**: 添加更多NPU操作的测试
2. **性能基准**: 建立NPU性能基准测试
3. **自动化CI**: 集成到持续集成流程
4. **文档完善**: 根据实际使用情况更新文档

## 联系和支持

如果在使用过程中遇到问题，可以：

1. 查看详细的 `NPU_TEST_README.md` 文档
2. 检查测试配置文件 `npu_test_config.h`
3. 参考现有的测试样例代码
4. 查看CSI-NN2官方文档和API参考

---

**注意**: 这些测试样例基于CSI-NN2框架和TH1520 NPU设计，在实际使用前请确保硬件环境和软件依赖满足要求。
