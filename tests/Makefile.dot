# NPU向量点乘测试Makefile

CC = /home/<USER>/llvm/bin/clang
INCLUDE = -I../include
CFLAGS = -O2 -g
LDFLAGS = -L../include/venv/lib/python3.13/site-packages/shl/install_nn2/th1520/lib
LIBS = -lshl_th1520 -lm

# 编译NPU向量点乘测试
all: simple_npu_dot_product

simple_npu_dot_product: simple_npu_dot_product.c
	$(CC) $(CFLAGS) $(INCLUDE) $(LDFLAGS) $< $(LIBS) -o $@

# 清理
clean:
	rm -f simple_npu_dot_product *.o

.PHONY: all clean
